import hashlib
import http
from typing import Op<PERSON>, <PERSON><PERSON>

from blitzy_utils.logger import logger
from cachetools import TTLCache
from common_models.models import GithubInstallationStatus, VersionControlSystem
from flask import Blueprint
from flask_utils.decorators import flask_pydantic_response, validate_request

from src.api.models import AzureInstallation, SecretsOutput
from src.api.utils.secret_manager_utils import create_or_update_azure_secret
from src.error.errors import AzureAlreadyIntegrated
from src.service.azure_service import (get_tenant_id_from_token,
                                       perform_exchange)
from src.service.git_installation_service import (
    create_new_git_installation,
    get_active_git_integration_by_installation_and_svc_type)

azure_bp = Blueprint("azure_bp", __name__, url_prefix="/v1/azure")

# Authorization code cache with 10-minute TTL to prevent code reuse
# Authorization codes are typically valid for 10 minutes, so we cache for the same duration
auth_code_cache: TTLCache = TTLCache(maxsize=1000, ttl=600)


def get_cached_token_exchange(auth_code: str, redirect_uri: str) -> Optional[Tuple[str, str]]:
    """
    Get cached token exchange result if it exists.

    :param auth_code: The authorization code
    :param redirect_uri: The redirect URI used in the exchange
    :return: Tuple of (access_token, refresh_token) if cached, None otherwise
    """
    cache_key = f"auth_code_{hashlib.sha256(f'{auth_code}_{redirect_uri}'.encode()).hexdigest()}"
    cached_result = auth_code_cache.get(cache_key)

    if cached_result is None:
        logger.debug("No cached token exchange found for auth code")
        return None

    access_token, refresh_token = cached_result
    logger.debug("Using cached token exchange result")
    return access_token, refresh_token


def cache_token_exchange(auth_code: str, redirect_uri: str, access_token: str, refresh_token: str) -> None:
    """
    Cache the result of a token exchange to prevent duplicate exchanges.

    :param auth_code: The authorization code that was exchanged
    :param redirect_uri: The redirect URI used in the exchange
    :param access_token: The access token received
    :param refresh_token: The refresh token received
    """
    cache_key = f"auth_code_{hashlib.sha256(f'{auth_code}_{redirect_uri}'.encode()).hexdigest()}"
    auth_code_cache[cache_key] = (access_token, refresh_token)
    logger.debug("Cached token exchange result for auth code")


def perform_exchange_with_cache(auth_code: str, redirect_uri: str) -> Tuple[str, str]:
    """
    Perform OAuth2 authorization code exchange with caching to prevent duplicate exchanges.

    :param auth_code: The authorization code to exchange
    :param redirect_uri: The redirect URI used in the exchange
    :return: Tuple of (access_token, refresh_token)
    :raises: Any exception from perform_exchange if token exchange fails
    """
    # First, try to get cached result
    cached_result = get_cached_token_exchange(auth_code, redirect_uri)
    if cached_result:
        return cached_result

    # No cached result, perform the actual exchange
    logger.info("Performing new OAuth2 token exchange for auth code")
    access_token, refresh_token = perform_exchange(auth_code, redirect_uri)

    # Cache the result
    cache_token_exchange(auth_code, redirect_uri, access_token, refresh_token)

    return access_token, refresh_token


@azure_bp.route("/install", methods=["POST"])
@validate_request(AzureInstallation)
@flask_pydantic_response
def post_install(payload: AzureInstallation):
    logger.info(
        f"Handling request for a new azure installation for user {payload.user_id}"
    )
    access_token, refresh_token = perform_exchange_with_cache(payload.code, payload.redirect_uri)
    logger.info("Got access and refresh tokens")
    tenant_id = get_tenant_id_from_token(access_token)

    active_git_installation = get_active_git_integration_by_installation_and_svc_type(
        tenant_id, VersionControlSystem.AZURE_DEVOPS
    )

    if active_git_installation:
        msg = (
            f"Can't add azure integration for user {payload.user_id} because there is already "
            f"an active AZURE git integration {active_git_installation.id} "
            f"for this organization {active_git_installation.installation_id}."
        )
        logger.info(msg)
        raise AzureAlreadyIntegrated(message=msg)
    else:
        logger.info(f"No active git integration found for tenant {tenant_id} and user {payload.user_id}")

    create_or_update_azure_secret(
        access_token,
        refresh_token,
        tenant_id,
        payload.redirect_uri,
        payload.requested_scope,
    )
    logger.info(
        f"Created or updated secret for azure installation for tenant {tenant_id} and user {payload.user_id}"
    )

    installation_id = create_new_git_installation(
        user_id=payload.user_id,
        status=GithubInstallationStatus.ACTIVE,
        installation_id=tenant_id,
        target_id=tenant_id,
        target_name="",
        svc_type=VersionControlSystem.AZURE_DEVOPS,
        metadata={},
    )

    logger.info(f"Created new git installation {installation_id}  for tenant {tenant_id} and user {payload.user_id}")
    return (SecretsOutput(
        accessToken=access_token,
        code=payload.code,
        installationID=tenant_id,
        setupAction="azure_setup",
        scvType=VersionControlSystem.AZURE_DEVOPS.value,
    ), http.HTTPStatus.CREATED)
