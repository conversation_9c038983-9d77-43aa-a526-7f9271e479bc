"""
Integration tests for Azure install endpoint authorization code caching functionality.
Tests the actual caching implementation in src.api.routes.azure to prevent
"OAuth2 Authorization code was already redeemed" errors.
"""

import hashlib
import unittest
from unittest.mock import MagicMock, call, patch

# Import the actual Azure routes module to test the real implementation
try:
    from main import app  # Import Flask app for context
except ImportError:
    app = None

# Mock external dependencies but import the real Azure routes
with patch.dict('sys.modules', {
    'blitzy_utils.logger': MagicMock(),
    'common_models.models': MagicMock(),
    'src.api.models': MagicMock(),
    'src.api.utils.secret_manager_utils': MagicMock(),
    'src.error.errors': MagicMock(),
    'src.service.azure_service': MagicMock(),
    'src.service.git_installation_service': MagicMock(),
}):
    # Import the actual Azure routes functions
    from src.api.routes.azure import (auth_code_cache, cache_token_exchange,
                                      get_cached_token_exchange,
                                      perform_exchange_with_cache)


class TestAzureInstallCaching(unittest.TestCase):
    """Test cases for Azure install endpoint authorization code caching."""

    def setUp(self):
        """Set up test fixtures."""
        # Clear the actual cache before each test
        auth_code_cache.clear()

        # Mock the perform_exchange function where it's imported in the azure routes module
        self.perform_exchange_patcher = patch('src.api.routes.azure.perform_exchange')
        self.mock_perform_exchange = self.perform_exchange_patcher.start()
        self.mock_perform_exchange.return_value = ("mock_access_token", "mock_refresh_token")

    def tearDown(self):
        """Clean up test fixtures."""
        self.perform_exchange_patcher.stop()
        auth_code_cache.clear()

    def test_cache_miss_performs_exchange(self):
        """Test that cache miss triggers actual token exchange."""
        auth_code = "test_auth_code_123"
        redirect_uri = "https://example.com/callback"

        # Clear cache to ensure miss
        auth_code_cache.clear()

        # Call the function
        access_token, refresh_token = perform_exchange_with_cache(auth_code, redirect_uri)

        # Verify perform_exchange was called
        self.mock_perform_exchange.assert_called_once_with(auth_code, redirect_uri)

        # Verify tokens returned
        self.assertEqual(access_token, "mock_access_token")
        self.assertEqual(refresh_token, "mock_refresh_token")

        # Verify result was cached
        cache_key = f"auth_code_{hashlib.sha256(f'{auth_code}_{redirect_uri}'.encode()).hexdigest()}"
        self.assertIn(cache_key, auth_code_cache)
        self.assertEqual(auth_code_cache[cache_key], ("mock_access_token", "mock_refresh_token"))

    def test_cache_hit_reuses_tokens(self):
        """Test that cache hit reuses existing tokens without calling perform_exchange."""
        auth_code = "test_auth_code_456"
        redirect_uri = "https://example.com/callback"

        # Pre-populate cache
        cached_access = "cached_access_token"
        cached_refresh = "cached_refresh_token"
        cache_token_exchange(auth_code, redirect_uri, cached_access, cached_refresh)

        # Call the function
        access_token, refresh_token = perform_exchange_with_cache(auth_code, redirect_uri)

        # Verify perform_exchange was NOT called
        self.mock_perform_exchange.assert_not_called()

        # Verify cached tokens returned
        self.assertEqual(access_token, cached_access)
        self.assertEqual(refresh_token, cached_refresh)

    def test_different_auth_codes_separate_cache_entries(self):
        """Test that different authorization codes get separate cache entries."""
        auth_code1 = "auth_code_1"
        auth_code2 = "auth_code_2"
        redirect_uri = "https://example.com/callback"

        # Mock different return values for different calls
        self.mock_perform_exchange.side_effect = [
            ("access_1", "refresh_1"),
            ("access_2", "refresh_2")
        ]

        # Call with first auth code
        access1, refresh1 = perform_exchange_with_cache(auth_code1, redirect_uri)

        # Call with second auth code
        access2, refresh2 = perform_exchange_with_cache(auth_code2, redirect_uri)

        # Verify both calls to perform_exchange
        self.assertEqual(self.mock_perform_exchange.call_count, 2)
        self.mock_perform_exchange.assert_has_calls([
            call(auth_code1, redirect_uri),
            call(auth_code2, redirect_uri)
        ])

        # Verify different tokens returned
        self.assertEqual(access1, "access_1")
        self.assertEqual(refresh1, "refresh_1")
        self.assertEqual(access2, "access_2")
        self.assertEqual(refresh2, "refresh_2")

        # Verify separate cache entries
        cache_key1 = f"auth_code_{hashlib.sha256(f'{auth_code1}_{redirect_uri}'.encode()).hexdigest()}"
        cache_key2 = f"auth_code_{hashlib.sha256(f'{auth_code2}_{redirect_uri}'.encode()).hexdigest()}"

        self.assertIn(cache_key1, auth_code_cache)
        self.assertIn(cache_key2, auth_code_cache)
        self.assertNotEqual(cache_key1, cache_key2)

    def test_different_redirect_uris_separate_cache_entries(self):
        """Test that different redirect URIs get separate cache entries."""
        auth_code = "same_auth_code"
        redirect_uri1 = "https://example1.com/callback"
        redirect_uri2 = "https://example2.com/callback"

        # Mock different return values
        self.mock_perform_exchange.side_effect = [
            ("access_1", "refresh_1"),
            ("access_2", "refresh_2")
        ]

        # Call with first redirect URI
        access1, refresh1 = perform_exchange_with_cache(auth_code, redirect_uri1)

        # Call with second redirect URI
        access2, refresh2 = perform_exchange_with_cache(auth_code, redirect_uri2)

        # Verify both calls to perform_exchange
        self.assertEqual(self.mock_perform_exchange.call_count, 2)

        # Verify separate cache entries
        cache_key1 = f"auth_code_{hashlib.sha256(f'{auth_code}_{redirect_uri1}'.encode()).hexdigest()}"
        cache_key2 = f"auth_code_{hashlib.sha256(f'{auth_code}_{redirect_uri2}'.encode()).hexdigest()}"

        self.assertIn(cache_key1, auth_code_cache)
        self.assertIn(cache_key2, auth_code_cache)
        self.assertNotEqual(cache_key1, cache_key2)

    def test_cache_key_generation(self):
        """Test that cache keys are generated correctly."""
        auth_code = "test_code"
        redirect_uri = "test_uri"

        # Generate expected cache key
        expected_key = f"auth_code_{hashlib.sha256(f'{auth_code}_{redirect_uri}'.encode()).hexdigest()}"

        # Cache a token
        cache_token_exchange(auth_code, redirect_uri, "test_access", "test_refresh")

        # Verify cache key exists
        self.assertIn(expected_key, auth_code_cache)
        self.assertEqual(auth_code_cache[expected_key], ("test_access", "test_refresh"))

    def test_cache_isolation_prevents_cross_contamination(self):
        """Test that cache entries don't interfere with each other."""
        # Set up multiple different combinations
        combinations = [
            ("code1", "uri1", "access1", "refresh1"),
            ("code1", "uri2", "access2", "refresh2"),
            ("code2", "uri1", "access3", "refresh3"),
            ("code2", "uri2", "access4", "refresh4"),
        ]

        # Cache all combinations
        for auth_code, redirect_uri, access_token, refresh_token in combinations:
            cache_token_exchange(auth_code, redirect_uri, access_token, refresh_token)

        # Verify each combination can be retrieved correctly
        for auth_code, redirect_uri, expected_access, expected_refresh in combinations:
            cached_result = get_cached_token_exchange(auth_code, redirect_uri)
            self.assertIsNotNone(cached_result)
            access_token, refresh_token = cached_result
            self.assertEqual(access_token, expected_access)
            self.assertEqual(refresh_token, expected_refresh)

    def test_multiple_requests_same_code_use_cache(self):
        """Test that multiple requests with the same auth code use cached result."""
        auth_code = "repeated_code"
        redirect_uri = "https://example.com/callback"

        # First call should hit perform_exchange
        access1, refresh1 = perform_exchange_with_cache(auth_code, redirect_uri)

        # Reset mock to track subsequent calls
        self.mock_perform_exchange.reset_mock()

        # Second call should use cache
        access2, refresh2 = perform_exchange_with_cache(auth_code, redirect_uri)

        # Third call should also use cache
        access3, refresh3 = perform_exchange_with_cache(auth_code, redirect_uri)

        # Verify perform_exchange was not called again
        self.mock_perform_exchange.assert_not_called()

        # Verify all calls return same tokens
        self.assertEqual(access1, access2)
        self.assertEqual(access1, access3)
        self.assertEqual(refresh1, refresh2)
        self.assertEqual(refresh1, refresh3)

    def test_empty_cache_returns_none(self):
        """Test that empty cache returns None for get_cached_token_exchange."""
        auth_code = "nonexistent_code"
        redirect_uri = "https://example.com/callback"

        # Clear cache
        auth_code_cache.clear()

        # Try to get cached result
        result = get_cached_token_exchange(auth_code, redirect_uri)

        # Should return None
        self.assertIsNone(result)


def run_azure_install_caching_tests():
    """Run all Azure install caching tests."""
    print("🧪 Running Azure Install Endpoint Caching Tests")
    print("=" * 60)

    # Create test suite
    suite = unittest.TestLoader().loadTestsFromTestCase(TestAzureInstallCaching)

    # Run tests with detailed output
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)

    # Print summary
    print("\n" + "=" * 60)
    print("TEST SUMMARY")
    print("=" * 60)
    print(f"Tests run: {result.testsRun}")
    print(f"Failures: {len(result.failures)}")
    print(f"Errors: {len(result.errors)}")

    if result.failures:
        print("\nFAILURES:")
        for test, traceback in result.failures:
            print(f"  • {test}: {traceback}")

    if result.errors:
        print("\nERRORS:")
        for test, traceback in result.errors:
            print(f"  • {test}: {traceback}")

    success = len(result.failures) == 0 and len(result.errors) == 0

    if success:
        print("\n🎉 All tests passed!")
        print("\n📋 What this validates:")
        print("   • Authorization code caching prevents duplicate Azure API calls")
        print("   • Cache miss triggers actual token exchange")
        print("   • Cache hit reuses existing tokens")
        print("   • Different auth codes get separate cache entries")
        print("   • Different redirect URIs get separate cache entries")
        print("   • Multiple requests with same code use cached result")
        print("   • This should resolve 'OAuth2 Authorization code already redeemed' errors")
    else:
        print("\n❌ Some tests failed!")

    return success


if __name__ == "__main__":
    run_azure_install_caching_tests()
